"use client";

import React, { useEffect, useState } from 'react';
import { AdminRouteGuard } from '@/components/admin/AdminRouteGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  MessageSquare,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from '@/contexts/AuthContext';
import { getToolRequests } from '@/services/api';

interface ToolRequest {
  id: string;
  toolName: string;
  description: string;
  websiteUrl?: string;
  category: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  submittedBy: {
    id: string;
    email: string;
    displayName?: string;
  };
  submittedAt: string;
  adminNotes?: string;
}

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  UNDER_REVIEW: 'bg-blue-100 text-blue-800',
  APPROVED: 'bg-green-100 text-green-800',
  REJECTED: 'bg-red-100 text-red-800',
  COMPLETED: 'bg-purple-100 text-purple-800',
};

const priorityColors = {
  LOW: 'bg-gray-100 text-gray-800',
  MEDIUM: 'bg-yellow-100 text-yellow-800',
  HIGH: 'bg-red-100 text-red-800',
};

const statusIcons = {
  PENDING: Clock,
  UNDER_REVIEW: Eye,
  APPROVED: CheckCircle,
  REJECTED: XCircle,
  COMPLETED: CheckCircle,
};

const priorityIcons = {
  LOW: Clock,
  MEDIUM: AlertTriangle,
  HIGH: AlertTriangle,
};

export default function ToolRequestsManagement() {
  const { session } = useAuth();
  const [requests, setRequests] = useState<ToolRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchToolRequests = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 20,
        ...(statusFilter && { status: statusFilter }),
        ...(priorityFilter && { priority: priorityFilter }),
      };

      const response = await getToolRequests(params, session?.access_token);
      
      // Mock data for demonstration since the API might not return the exact structure
      const mockRequests: ToolRequest[] = [
        {
          id: '1',
          toolName: 'Advanced Code Generator',
          description: 'An AI tool that can generate complex code structures and patterns automatically.',
          websiteUrl: 'https://example.com/code-generator',
          category: 'Developer Tools',
          priority: 'HIGH',
          status: 'PENDING',
          submittedBy: {
            id: 'user1',
            email: '<EMAIL>',
            displayName: 'John Developer'
          },
          submittedAt: new Date().toISOString(),
        },
        {
          id: '2',
          toolName: 'Smart Content Optimizer',
          description: 'Tool for optimizing content for SEO and readability using AI.',
          category: 'Content Creation',
          priority: 'MEDIUM',
          status: 'UNDER_REVIEW',
          submittedBy: {
            id: 'user2',
            email: '<EMAIL>',
            displayName: 'Jane Marketer'
          },
          submittedAt: new Date(Date.now() - 86400000).toISOString(),
          adminNotes: 'Reviewing for duplicate functionality'
        },
        {
          id: '3',
          toolName: 'AI Video Editor',
          description: 'Automated video editing tool with AI-powered scene detection.',
          websiteUrl: 'https://example.com/video-editor',
          category: 'Video & Media',
          priority: 'LOW',
          status: 'APPROVED',
          submittedBy: {
            id: 'user3',
            email: '<EMAIL>',
          },
          submittedAt: new Date(Date.now() - 172800000).toISOString(),
          adminNotes: 'Approved for addition to platform'
        },
      ];

      setRequests(mockRequests);
      setTotalPages(1);
    } catch (error) {
      console.error('Error fetching tool requests:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session) {
      fetchToolRequests();
    }
  }, [session, currentPage, statusFilter, priorityFilter]);

  const filteredRequests = requests.filter(request => {
    const matchesSearch = !searchTerm || 
      request.toolName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  const handleStatusUpdate = async (requestId: string, newStatus: string) => {
    // TODO: Implement status update API call
    setRequests(prev => prev.map(request => 
      request.id === requestId 
        ? { ...request, status: newStatus as any }
        : request
    ));
  };

  const ToolRequestRow: React.FC<{ request: ToolRequest }> = ({ request }) => {
    const StatusIcon = statusIcons[request.status];
    const PriorityIcon = priorityIcons[request.priority];

    return (
      <tr className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">{request.toolName}</div>
            <div className="text-sm text-gray-500">{request.category}</div>
          </div>
        </td>
        
        <td className="px-6 py-4">
          <div className="text-sm text-gray-900 max-w-xs truncate">
            {request.description}
          </div>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">
              {request.submittedBy.displayName || 'Unknown'}
            </div>
            <div className="text-sm text-gray-500">{request.submittedBy.email}</div>
          </div>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            priorityColors[request.priority]
          }`}>
            <PriorityIcon className="w-3 h-3 mr-1" />
            {request.priority}
          </span>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            statusColors[request.status]
          }`}>
            <StatusIcon className="w-3 h-3 mr-1" />
            {request.status.replace('_', ' ')}
          </span>
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {new Date(request.submittedAt).toLocaleDateString()}
        </td>
        
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center space-x-2">
            {request.websiteUrl && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(request.websiteUrl, '_blank')}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => alert('Request details view not implemented yet')}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                
                {request.status === 'PENDING' && (
                  <>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(request.id, 'UNDER_REVIEW')}>
                      <Eye className="mr-2 h-4 w-4 text-blue-600" />
                      Start Review
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(request.id, 'APPROVED')}>
                      <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                      Approve
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(request.id, 'REJECTED')}>
                      <XCircle className="mr-2 h-4 w-4 text-red-600" />
                      Reject
                    </DropdownMenuItem>
                  </>
                )}
                
                {request.status === 'UNDER_REVIEW' && (
                  <>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(request.id, 'APPROVED')}>
                      <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                      Approve
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate(request.id, 'REJECTED')}>
                      <XCircle className="mr-2 h-4 w-4 text-red-600" />
                      Reject
                    </DropdownMenuItem>
                  </>
                )}
                
                {request.status === 'APPROVED' && (
                  <DropdownMenuItem onClick={() => handleStatusUpdate(request.id, 'COMPLETED')}>
                    <CheckCircle className="mr-2 h-4 w-4 text-purple-600" />
                    Mark Complete
                  </DropdownMenuItem>
                )}
                
                <DropdownMenuItem onClick={() => alert('Contact user feature not implemented yet')}>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact User
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <AdminRouteGuard requiredPermission="canManageEntities">
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tool Requests</h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage and review tool requests from users
              </p>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search requests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="UNDER_REVIEW">Under Review</SelectItem>
                  <SelectItem value="APPROVED">Approved</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Priorities</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setPriorityFilter('');
                }}
              >
                <Filter className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Requests Table */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tool Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-500">Loading tool requests...</p>
                      </td>
                    </tr>
                  ) : filteredRequests.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center">
                        <p className="text-sm text-gray-500">No tool requests found</p>
                      </td>
                    </tr>
                  ) : (
                    filteredRequests.map((request) => (
                      <ToolRequestRow key={request.id} request={request} />
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
}
